# SSH密钥认证自动配置脚本
# 运行前请确保以管理员权限执行PowerShell

Write-Host "==================================" -ForegroundColor Green
Write-Host "SSH密钥认证配置脚本" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host ""

# 检查是否以管理员权限运行
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误: 请以管理员权限运行此脚本!" -ForegroundColor Red
    Write-Host "右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 步骤1: 检查SSH服务状态
Write-Host "1. 检查SSH服务状态..." -ForegroundColor Cyan
$sshService = Get-Service -Name sshd -ErrorAction SilentlyContinue
if ($sshService -and $sshService.Status -eq "Running") {
    Write-Host "   ✓ SSH服务正在运行" -ForegroundColor Green
} else {
    Write-Host "   ✗ SSH服务未运行，请先启动SSH服务" -ForegroundColor Red
    exit 1
}

# 步骤2: 创建.ssh目录
Write-Host "2. 创建SSH目录..." -ForegroundColor Cyan
$sshDir = "$env:USERPROFILE\.ssh"
if (!(Test-Path $sshDir)) {
    New-Item -ItemType Directory -Path $sshDir -Force | Out-Null
    Write-Host "   ✓ 创建目录: $sshDir" -ForegroundColor Green
} else {
    Write-Host "   ✓ SSH目录已存在: $sshDir" -ForegroundColor Green
}

# 步骤3: 生成SSH密钥对
Write-Host "3. 生成SSH密钥对..." -ForegroundColor Cyan
$keyPath = "$sshDir\id_rsa"
if (Test-Path $keyPath) {
    Write-Host "   ! 密钥已存在，是否覆盖? (y/N): " -ForegroundColor Yellow -NoNewline
    $overwrite = Read-Host
    if ($overwrite -ne "y" -and $overwrite -ne "Y") {
        Write-Host "   → 跳过密钥生成" -ForegroundColor Yellow
        goto ConfigureKeys
    }
}

Write-Host "   → 正在生成4096位RSA密钥..." -ForegroundColor Yellow
$email = Read-Host "   请输入邮箱地址 (可选，直接回车跳过)"
if ([string]::IsNullOrEmpty($email)) {
    $email = "home-pc-access"
}

# 生成密钥
ssh-keygen -t rsa -b 4096 -f $keyPath -C $email -N '""'

if (Test-Path $keyPath) {
    Write-Host "   ✓ 密钥生成成功" -ForegroundColor Green
    Write-Host "   → 私钥: $keyPath" -ForegroundColor Gray
    Write-Host "   → 公钥: $keyPath.pub" -ForegroundColor Gray
} else {
    Write-Host "   ✗ 密钥生成失败" -ForegroundColor Red
    exit 1
}

:ConfigureKeys
# 步骤4: 配置公钥认证
Write-Host "4. 配置公钥认证..." -ForegroundColor Cyan
$authorizedKeysPath = "$env:ProgramData\ssh\administrators_authorized_keys"
$publicKeyPath = "$keyPath.pub"

if (Test-Path $publicKeyPath) {
    $publicKeyContent = Get-Content $publicKeyPath -Raw
    $publicKeyContent | Out-File -FilePath $authorizedKeysPath -Encoding ascii -NoNewline
    Write-Host "   ✓ 公钥已配置到: $authorizedKeysPath" -ForegroundColor Green
    
    # 设置正确的权限
    icacls $authorizedKeysPath /inheritance:r | Out-Null
    icacls $authorizedKeysPath /grant "Administrators:F" | Out-Null
    icacls $authorizedKeysPath /grant "SYSTEM:F" | Out-Null
    Write-Host "   ✓ 文件权限已设置" -ForegroundColor Green
} else {
    Write-Host "   ✗ 找不到公钥文件: $publicKeyPath" -ForegroundColor Red
    exit 1
}

# 步骤5: 备份并修改SSH配置
Write-Host "5. 配置SSH服务器..." -ForegroundColor Cyan
$sshdConfigPath = "C:\ProgramData\ssh\sshd_config"
$backupPath = "$sshdConfigPath.backup.$(Get-Date -Format 'yyyyMMdd-HHmmss')"

# 备份原配置
Copy-Item $sshdConfigPath $backupPath
Write-Host "   ✓ 配置已备份到: $backupPath" -ForegroundColor Green

# 读取当前配置
$config = Get-Content $sshdConfigPath

# 检查并修改配置
$needsRestart = $false
$newConfig = @()

foreach ($line in $config) {
    if ($line -match "^#?PubkeyAuthentication") {
        $newConfig += "PubkeyAuthentication yes"
        $needsRestart = $true
        Write-Host "   ✓ 启用公钥认证" -ForegroundColor Green
    } elseif ($line -match "^#?AuthorizedKeysFile" -and $line -notmatch "__PROGRAMDATA__") {
        $newConfig += $line
        # 添加管理员用户的配置
        if ($newConfig -notcontains "Match Group administrators") {
            $newConfig += ""
            $newConfig += "Match Group administrators"
            $newConfig += "       AuthorizedKeysFile __PROGRAMDATA__/ssh/administrators_authorized_keys"
            $needsRestart = $true
            Write-Host "   ✓ 配置管理员用户密钥文件" -ForegroundColor Green
        }
    } else {
        $newConfig += $line
    }
}

# 如果没有找到Match Group administrators配置，添加它
if ($newConfig -notcontains "Match Group administrators") {
    $newConfig += ""
    $newConfig += "Match Group administrators"
    $newConfig += "       AuthorizedKeysFile __PROGRAMDATA__/ssh/administrators_authorized_keys"
    $needsRestart = $true
    Write-Host "   ✓ 添加管理员用户配置" -ForegroundColor Green
}

# 写入新配置
$newConfig | Out-File -FilePath $sshdConfigPath -Encoding ascii

# 步骤6: 重启SSH服务
if ($needsRestart) {
    Write-Host "6. 重启SSH服务..." -ForegroundColor Cyan
    Restart-Service sshd
    Write-Host "   ✓ SSH服务已重启" -ForegroundColor Green
} else {
    Write-Host "6. SSH配置无需更改" -ForegroundColor Cyan
}

# 步骤7: 测试连接
Write-Host "7. 测试SSH密钥认证..." -ForegroundColor Cyan
Write-Host "   → 正在测试本地连接..." -ForegroundColor Yellow

$testResult = ssh -i $keyPath -o BatchMode=yes -o ConnectTimeout=5 lenovo@localhost "echo 'SSH密钥认证成功!'"
if ($LASTEXITCODE -eq 0) {
    Write-Host "   ✓ SSH密钥认证测试成功!" -ForegroundColor Green
} else {
    Write-Host "   ⚠ SSH密钥认证测试失败，请检查配置" -ForegroundColor Yellow
    Write-Host "   → 可以尝试手动测试: ssh -i $keyPath lenovo@localhost" -ForegroundColor Gray
}

# 显示配置总结
Write-Host ""
Write-Host "==================================" -ForegroundColor Green
Write-Host "配置完成总结" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green
Write-Host "私钥文件: $keyPath" -ForegroundColor White
Write-Host "公钥文件: $publicKeyPath" -ForegroundColor White
Write-Host "授权密钥: $authorizedKeysPath" -ForegroundColor White
Write-Host ""
Write-Host "下一步操作:" -ForegroundColor Yellow
Write-Host "1. 将私钥文件导入到Termius应用中" -ForegroundColor White
Write-Host "2. 在Termius连接设置中选择密钥认证" -ForegroundColor White
Write-Host "3. 配置路由器端口转发以支持外网访问" -ForegroundColor White
Write-Host "4. 测试成功后可以禁用密码认证" -ForegroundColor White
Write-Host ""

# 显示公钥内容，方便复制到其他地方
Write-Host "公钥内容 (可复制到其他设备):" -ForegroundColor Cyan
Write-Host "----------------------------------------" -ForegroundColor Gray
Get-Content $publicKeyPath
Write-Host "----------------------------------------" -ForegroundColor Gray

Write-Host ""
Write-Host "配置完成! 按任意键退出..." -ForegroundColor Green
pause
