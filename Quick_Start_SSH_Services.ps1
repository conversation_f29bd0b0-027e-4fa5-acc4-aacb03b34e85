# SSH服务一键启动脚本 (PowerShell版本)
# 需要管理员权限运行

param(
    [switch]$Silent = $false
)

# 设置控制台编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8

function Write-Status {
    param($Message, $Status)
    $timestamp = Get-Date -Format "HH:mm:ss"
    switch ($Status) {
        "Success" { Write-Host "[$timestamp] ✅ $Message" -ForegroundColor Green }
        "Warning" { Write-Host "[$timestamp] ⚠️  $Message" -ForegroundColor Yellow }
        "Error"   { Write-Host "[$timestamp] ❌ $Message" -ForegroundColor Red }
        "Info"    { Write-Host "[$timestamp] ℹ️  $Message" -ForegroundColor Cyan }
        default   { Write-Host "[$timestamp] $Message" }
    }
}

function Test-AdminRights {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

function Start-ServiceSafely {
    param($ServiceName, $DisplayName)
    
    try {
        $service = Get-Service -Name $ServiceName -ErrorAction SilentlyContinue
        if ($null -eq $service) {
            Write-Status "$DisplayName 服务未安装" "Error"
            return $false
        }
        
        if ($service.Status -eq "Running") {
            Write-Status "$DisplayName 已在运行" "Info"
            return $true
        }
        
        Start-Service -Name $ServiceName -ErrorAction Stop
        Write-Status "$DisplayName 启动成功" "Success"
        return $true
    }
    catch {
        Write-Status "$DisplayName 启动失败: $($_.Exception.Message)" "Error"
        return $false
    }
}

# 主程序开始
Clear-Host

if (-not $Silent) {
    Write-Host @"

========================================
          SSH服务一键启动
========================================

"@ -ForegroundColor Cyan
}

# 检查管理员权限
Write-Status "检查管理员权限..." "Info"
if (-not (Test-AdminRights)) {
    Write-Status "需要管理员权限，正在重新启动..." "Warning"
    if (-not $Silent) {
        Start-Process PowerShell -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
    }
    exit 1
}
Write-Status "管理员权限确认" "Success"

# 启动SSH服务
Write-Status "启动SSH服务..." "Info"
$sshResult = Start-ServiceSafely -ServiceName "sshd" -DisplayName "SSH"

# 启动Tailscale服务
Write-Status "启动Tailscale服务..." "Info"
$tailscaleResult = Start-ServiceSafely -ServiceName "Tailscale" -DisplayName "Tailscale"

# 等待服务完全启动
Start-Sleep -Seconds 2

# 检查服务状态
Write-Status "检查服务状态..." "Info"

$sshService = Get-Service -Name "sshd" -ErrorAction SilentlyContinue
$tailscaleService = Get-Service -Name "Tailscale" -ErrorAction SilentlyContinue

if ($sshService -and $sshService.Status -eq "Running") {
    Write-Status "SSH服务: 运行中" "Success"
} else {
    Write-Status "SSH服务: 未运行" "Error"
}

if ($tailscaleService -and $tailscaleService.Status -eq "Running") {
    Write-Status "Tailscale服务: 运行中" "Success"
} else {
    Write-Status "Tailscale服务: 未运行" "Error"
}

# 检查Tailscale网络状态
Write-Status "检查Tailscale网络状态..." "Info"
try {
    $tailscaleStatus = & "C:\Program Files\Tailscale\tailscale.exe" status 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Tailscale网络状态:" -ForegroundColor Yellow
        $tailscaleStatus | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
        
        # 提取本机IP
        $myIP = ($tailscaleStatus | Select-String "windows.*idle" | ForEach-Object { $_.Line.Split()[0] })
        if ($myIP) {
            Write-Status "本机Tailscale IP: $myIP" "Success"
        }
    } else {
        Write-Status "Tailscale未登录或连接失败" "Warning"
        Write-Host "  请运行: tailscale login" -ForegroundColor Yellow
    }
} catch {
    Write-Status "无法获取Tailscale状态" "Error"
}

# 检查SSH端口监听
Write-Status "检查SSH端口监听..." "Info"
$sshPort = netstat -an | Select-String ":22.*LISTENING"
if ($sshPort) {
    Write-Status "SSH端口22正在监听" "Success"
} else {
    Write-Status "SSH端口22未监听" "Error"
}

# 显示连接信息
if (-not $Silent) {
    Write-Host @"

========================================
           启动完成！
========================================

📱 iPhone连接信息:
   主机: ************* (如果Tailscale正常)
   端口: 22
   用户: lenovo
   认证: 密码

🔧 备用连接 (端口转发):
   主机: *************
   端口: 2222
   用户: lenovo
   认证: 密码

"@ -ForegroundColor Green

    Write-Host "按任意键退出..." -ForegroundColor Gray
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

# 返回状态码
if ($sshResult -and $tailscaleResult) {
    exit 0
} else {
    exit 1
}
