# 🔐 PEM密钥专用访问配置完成 (类似AWS EC2)

## ✅ 配置状态
- ✅ 生成了4096位RSA PEM格式密钥
- ✅ 完全禁用了密码认证
- ✅ 只允许密钥文件登录
- ✅ 配置了安全的SSH服务器设置
- ✅ 设置了用户访问限制

## 🔑 PEM密钥文件信息

### 密钥文件位置
- **私钥文件 (PEM格式)**: `C:\Users\<USER>\.ssh\home-pc-key.pem`
- **公钥文件**: `C:\Users\<USER>\.ssh\home-pc-key.pem.pub`
- **服务器授权文件**: `C:\ProgramData\ssh\administrators_authorized_keys`

### 密钥特征
- **格式**: PEM (-----BEGIN RSA PRIVATE KEY-----)
- **类型**: RSA 4096位
- **指纹**: SHA256:nhBIi7Q7pYQ5SobJSOPkLq2mLJ1PT02J05Wo3vpGF3w
- **注释**: home-pc-pem-access

## 🛡️ 安全配置详情

### SSH服务器安全设置
```bash
# 只允许公钥认证
PubkeyAuthentication yes
PasswordAuthentication no
ChallengeResponseAuthentication no
PermitEmptyPasswords no

# 用户限制
AllowUsers lenovo
MaxAuthTries 3
MaxSessions 2

# 其他安全设置
PermitRootLogin no
StrictModes yes
LoginGraceTime 60
X11Forwarding no
```

### 认证方式限制
- ❌ **密码认证**: 完全禁用
- ❌ **挑战响应认证**: 禁用
- ❌ **空密码**: 禁用
- ✅ **公钥认证**: 唯一允许的方式

## 📱 在Termius中配置PEM密钥

### 步骤1: 传输PEM文件到手机
1. **通过云盘**:
   - 将 `home-pc-key.pem` 上传到OneDrive/Google Drive
   - 在手机上下载文件

2. **通过邮件**:
   - 将PEM文件作为附件发送给自己
   - 在手机上保存附件

3. **通过数据线**:
   - 直接复制文件到手机存储

### 步骤2: 在Termius中导入PEM密钥
1. 打开Termius应用
2. 进入 **设置** → **密钥管理** (Keychain)
3. 点击 **"+"** → **导入密钥**
4. 选择 `home-pc-key.pem` 文件
5. 设置密钥名称: "Home PC PEM Key"
6. 如果提示输入密码短语，留空（我们生成时没有设置密码）

### 步骤3: 配置SSH连接
1. 编辑你的SSH连接
2. 连接设置：
   ```
   主机名: ************** (内网) 或 ************* (外网)
   端口: 22 (内网) 或 2222 (外网，需要路由器转发)
   用户名: lenovo
   认证方式: 密钥
   密钥: Home PC PEM Key
   ```

## 🌐 外网访问配置

### 路由器端口转发设置
1. 登录路由器: http://************
2. 配置端口转发：
   ```
   服务名称: SSH_PEM_Access
   外部端口: 2222
   内部端口: 22
   内部IP: **************
   协议: TCP
   状态: 启用
   ```

### Termius外网连接配置
```
主机名: *************
端口: 2222
用户名: lenovo
认证: PEM密钥 (Home PC PEM Key)
```

## 🧪 测试连接

### 本地测试命令
```bash
# 测试PEM密钥认证
ssh -i C:\Users\<USER>\.ssh\home-pc-key.pem lenovo@localhost

# 测试外网连接
ssh -i C:\Users\<USER>\.ssh\home-pc-key.pem -p 2222 lenovo@*************
```

### 验证密码认证已禁用
```bash
# 尝试密码登录（应该失败）
ssh lenovo@localhost
# 输出应该显示: Permission denied (publickey)
```

## 🔒 安全验证

### 确认配置正确性
1. **只支持公钥认证**: ✅
   ```
   debug1: Authentications that can continue: publickey
   ```

2. **服务器接受PEM密钥**: ✅
   ```
   debug1: Server accepts key: home-pc-key.pem RSA SHA256:nhBIi7Q7pYQ5SobJSOPkLq2mLJ1PT02J05Wo3vpGF3w
   ```

3. **密码认证被拒绝**: ✅
   ```
   Permission denied (publickey)
   ```

## 📋 重要提醒

### 🚨 安全注意事项
1. **备份PEM文件**: 私钥丢失将无法登录
2. **保护私钥**: 不要泄露给他人
3. **安全存储**: 建议加密存储PEM文件
4. **定期轮换**: 考虑定期更换密钥

### 💾 备份建议
```bash
# 备份密钥文件
Copy-Item "C:\Users\<USER>\.ssh\home-pc-key.pem" "D:\Backup\ssh-keys\"

# 备份SSH配置
Copy-Item "C:\ProgramData\ssh\sshd_config" "D:\Backup\ssh-config\"
```

## 🔄 多设备访问

### 为其他设备生成专用PEM密钥
```bash
# 为平板生成PEM密钥
ssh-keygen -t rsa -b 4096 -m PEM -f C:\Users\<USER>\.ssh\tablet-key.pem -C "tablet-access"

# 添加公钥到授权文件
type C:\Users\<USER>\.ssh\tablet-key.pem.pub >> C:\ProgramData\ssh\administrators_authorized_keys
```

## 🛠️ 故障排查

### 常见问题解决
1. **连接被拒绝**:
   - 检查SSH服务状态: `Get-Service sshd`
   - 验证防火墙设置
   - 确认端口转发配置

2. **密钥认证失败**:
   - 检查PEM文件格式
   - 验证公钥是否在authorized_keys中
   - 确认文件权限正确

3. **权限问题**:
   ```powershell
   # 重新设置权限
   icacls "C:\ProgramData\ssh\administrators_authorized_keys" /inheritance:r
   icacls "C:\ProgramData\ssh\administrators_authorized_keys" /grant "Administrators:F"
   icacls "C:\ProgramData\ssh\administrators_authorized_keys" /grant "SYSTEM:F"
   ```

## 🎯 配置验证清单

- [ ] PEM密钥文件已生成并保存
- [ ] 公钥已配置到authorized_keys
- [ ] SSH服务器配置已更新
- [ ] 密码认证已完全禁用
- [ ] 只允许公钥认证
- [ ] 用户访问限制已设置
- [ ] 在Termius中成功导入PEM密钥
- [ ] 内网连接测试成功
- [ ] 路由器端口转发已配置
- [ ] 外网连接测试成功
- [ ] PEM文件已安全备份

---

## 🎉 恭喜！

你现在拥有了与AWS EC2完全相同的安全访问方式：
- **只能通过PEM密钥文件登录**
- **完全禁用了密码认证**
- **具备企业级安全标准**

你的电脑现在就像一台专业的云服务器一样安全！🚀
