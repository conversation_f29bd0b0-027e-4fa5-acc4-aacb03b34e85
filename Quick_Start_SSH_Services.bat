@echo off
chcp 65001 >nul
title SSH服务一键启动

echo.
echo ========================================
echo           SSH服务一键启动
echo ========================================
echo.

echo [1/4] 检查管理员权限...
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ 管理员权限确认
) else (
    echo ❌ 需要管理员权限，正在重新启动...
    powershell -Command "Start-Process '%~f0' -Verb RunAs"
    exit /b
)

echo.
echo [2/4] 启动SSH服务...
net start sshd >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ SSH服务启动成功
) else (
    echo ⚠️  SSH服务已在运行或启动失败
)

echo.
echo [3/4] 启动Tailscale服务...
net start Tailscale >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Tailscale服务启动成功
) else (
    echo ⚠️  Tailscale服务已在运行或启动失败
)

echo.
echo [4/4] 检查服务状态...
echo.

echo SSH服务状态:
sc query sshd | findstr "STATE"

echo.
echo Tailscale服务状态:
sc query Tailscale | findstr "STATE"

echo.
echo Tailscale网络状态:
"C:\Program Files\Tailscale\tailscale.exe" status 2>nul
if %errorLevel% neq 0 (
    echo ⚠️  Tailscale未登录，请手动登录
)

echo.
echo ========================================
echo           启动完成！
echo ========================================
echo.
echo 📱 现在可以从iPhone连接:
echo    主机: *************
echo    端口: 22
echo    用户: lenovo
echo    认证: 密码
echo.

pause
