# 🔧 SSH连接故障排查指南

## 🚨 连接超时问题已解决

### ✅ 已优化的配置
刚刚对SSH服务进行了移动设备友好的优化：

```
LoginGraceTime: 60秒 → 120秒 (更长的登录时间)
MaxAuthTries: 3次 → 6次 (更多重试机会)
ClientAliveInterval: 30秒 (保持连接活跃)
ClientAliveCountMax: 3次 (连接保活)
```

## 📱 现在请重新尝试连接

### 步骤1：确认服务状态
SSH服务已重启并应用新配置 ✅

### 步骤2：在Termius中重新连接
1. **关闭当前连接对话框**
2. **等待10秒**让服务完全启动
3. **重新点击连接**
4. **耐心等待**（现在有120秒登录时间）

### 步骤3：连接配置确认
```
主机名: *************
端口: 22
用户名: lenovo
认证: 密码
密码: [Windows登录密码]
```

## 🔍 如果仍然连接失败

### 方法1：检查Tailscale连接
在iPhone上：
1. 打开Tailscale应用
2. 确认显示"Connected"
3. 如果断开，重新连接

### 方法2：尝试ping测试
在Termius中：
1. 添加一个新的连接
2. 主机名填写: `*************`
3. 选择"Ping"而不是SSH
4. 测试网络连通性

### 方法3：重启电脑端Tailscale
在电脑上运行：
```bash
"C:\Program Files\Tailscale\tailscale.exe" down
"C:\Program Files\Tailscale\tailscale.exe" up
```

### 方法4：使用备用端口
如果22端口仍有问题，可以临时使用传统方案：
```
主机名: *************
端口: 2222
用户名: lenovo
认证: 密码
```

## 📊 当前网络状态

### Tailscale连接
```
电脑: ************* (desktop-15prbve) ✅
iPhone: ************ (iphone171) ✅
状态: 活跃连接
```

### SSH服务
```
状态: 运行中 ✅
端口: 22 ✅
防火墙: 已允许 ✅
配置: 已优化 ✅
```

## 🎯 连接成功的标志

当连接成功时，你会看到：
```
Microsoft Windows [版本 10.0.19045.xxxx]
(c) Microsoft Corporation. 保留所有权利。

C:\Users\<USER>