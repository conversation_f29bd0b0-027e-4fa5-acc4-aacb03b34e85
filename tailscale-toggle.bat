@echo off
echo Tailscale 控制脚本
echo.
echo 1. 启动 Tailscale
echo 2. 停止 Tailscale
echo 3. 查看状态
echo.
set /p choice=请选择 (1-3): 

if "%choice%"=="1" (
    echo 启动 Tailscale...
    "C:\Program Files\Tailscale\tailscale.exe" up
    echo Tailscale 已启动
) else if "%choice%"=="2" (
    echo 停止 Tailscale...
    "C:\Program Files\Tailscale\tailscale.exe" down
    echo Tailscale 已停止
) else if "%choice%"=="3" (
    echo Tailscale 状态:
    "C:\Program Files\Tailscale\tailscale.exe" status
) else (
    echo 无效选择
)

pause
