# SSH密钥认证配置指南 (类似AWS EC2)

## 🔑 SSH密钥认证的优势
- 比密码更安全
- 无需记忆复杂密码
- 可以禁用密码登录，防止暴力破解
- 支持多设备管理

## 📱 方案一：在手机Termius中生成密钥 (推荐)

### 步骤1: 在Termius中生成密钥对
1. 打开Termius应用
2. 进入 **设置** → **密钥管理** (Keychain)
3. 点击 **"+"** → **新建密钥**
4. 选择密钥类型：
   ```
   类型: RSA
   长度: 4096 (推荐)
   名称: home-pc-key
   密码短语: [可选，建议设置]
   ```
5. 生成完成后，**导出公钥**

### 步骤2: 将公钥配置到Windows电脑
**方式A: 通过现有密码连接自动配置**
1. 先用密码连接到电脑
2. 在Termius连接设置中选择 **SSH密钥**
3. 选择刚生成的密钥
4. Termius会自动上传并配置公钥

**方式B: 手动配置公钥**
1. 从Termius导出公钥内容
2. 在电脑上执行以下命令配置

## 💻 方案二：在电脑上生成密钥对

### 步骤1: 生成SSH密钥对
```powershell
# 在PowerShell中执行
ssh-keygen -t rsa -b 4096 -C "home-pc-access"
```

执行后会提示：
```
Enter file in which to save the key: [直接回车使用默认路径]
Enter passphrase: [输入密码短语，可选但推荐]
Enter same passphrase again: [再次输入确认]
```

### 步骤2: 查看生成的密钥
```powershell
# 查看公钥内容
Get-Content $env:USERPROFILE\.ssh\id_rsa.pub

# 查看私钥位置
ls $env:USERPROFILE\.ssh\
```

## 🔧 配置Windows SSH服务器

### 步骤1: 配置公钥认证文件
```powershell
# 为管理员用户配置authorized_keys
$authorizedKeysPath = "$env:ProgramData\ssh\administrators_authorized_keys"

# 如果使用电脑生成的密钥
$publicKeyContent = Get-Content "$env:USERPROFILE\.ssh\id_rsa.pub"
$publicKeyContent | Out-File -FilePath $authorizedKeysPath -Encoding ascii

# 设置正确的权限
icacls $authorizedKeysPath /inheritance:r
icacls $authorizedKeysPath /grant "Administrators:F"
icacls $authorizedKeysPath /grant "SYSTEM:F"
```

### 步骤2: 修改SSH配置
```powershell
# 备份原配置
Copy-Item "C:\ProgramData\ssh\sshd_config" "C:\ProgramData\ssh\sshd_config.backup"

# 编辑配置文件
notepad "C:\ProgramData\ssh\sshd_config"
```

### 步骤3: 关键配置项
在sshd_config文件中确保以下设置：
```bash
# 启用公钥认证
PubkeyAuthentication yes

# 对于管理员用户使用这个文件
Match Group administrators
       AuthorizedKeysFile __PROGRAMDATA__/ssh/administrators_authorized_keys

# 可选：禁用密码认证（测试成功后）
# PasswordAuthentication no
```

### 步骤4: 重启SSH服务
```powershell
Restart-Service sshd
```

## 📲 在Termius中配置密钥认证

### 编辑连接设置
1. 在Termius中编辑你的连接
2. 认证方式改为 **"密钥"**
3. 选择之前生成的密钥
4. 保存设置

### 测试连接
1. 尝试连接，应该提示输入密钥密码短语（如果设置了）
2. 成功连接后不再需要Windows密码

## 🛡️ 安全加固

### 禁用密码认证（可选）
测试密钥认证成功后，可以完全禁用密码登录：
```bash
# 在sshd_config中设置
PasswordAuthentication no
ChallengeResponseAuthentication no
```

### 其他安全设置
```bash
# 限制登录尝试
MaxAuthTries 3

# 设置登录超时
LoginGraceTime 60

# 只允许特定用户
AllowUsers lenovo
```

## 🧪 测试和故障排查

### 测试本地连接
```powershell
# 测试密钥认证
ssh -i $env:USERPROFILE\.ssh\id_rsa lenovo@localhost
```

### 调试连接问题
```powershell
# 详细调试输出
ssh -v -i $env:USERPROFILE\.ssh\id_rsa lenovo@localhost

# 检查SSH服务状态
Get-Service sshd

# 查看SSH日志
Get-EventLog -LogName Application -Source sshd | Select-Object -First 5
```

### 常见问题解决
1. **权限问题**: 确保authorized_keys文件权限正确
2. **路径问题**: 管理员用户使用administrators_authorized_keys
3. **格式问题**: 公钥必须是一行，不能有换行

## 📋 完整配置检查清单

- [ ] 生成SSH密钥对
- [ ] 配置公钥到authorized_keys文件
- [ ] 设置正确的文件权限
- [ ] 修改sshd_config启用公钥认证
- [ ] 重启SSH服务
- [ ] 在Termius中配置密钥认证
- [ ] 测试密钥登录成功
- [ ] 配置路由器端口转发（外网访问）
- [ ] 测试外网密钥连接
- [ ] 可选：禁用密码认证
