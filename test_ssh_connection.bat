@echo off
echo ================================
echo SSH远程连接测试脚本
echo ================================
echo.

echo 1. 检查SSH服务状态...
powershell -Command "Get-Service sshd | Format-Table -AutoSize"
echo.

echo 2. 检查SSH端口监听...
netstat -an | findstr :22
echo.

echo 3. 当前公网IP地址...
curl -s ifconfig.me
echo.
echo.

echo 4. 内网IP地址...
ipconfig | findstr "IPv4"
echo.

echo 5. 检查防火墙规则...
powershell -Command "Get-NetFirewallRule -DisplayName '*SSH*' -ErrorAction SilentlyContinue | Select-Object DisplayName, Enabled, Direction | Format-Table -AutoSize"
echo.

echo ================================
echo 配置信息总结:
echo ================================
echo 内网IP: ************96
echo 公网IP: 
curl -s ifconfig.me
echo.
echo 路由器: ************
echo SSH端口: 22
echo 用户名: lenovo
echo.
echo Termius连接配置:
echo 主机名: 
curl -s ifconfig.me
echo 端口: 2222 (需要在路由器设置端口转发)
echo 用户名: lenovo
echo 密码: [你的Windows密码]
echo ================================

pause
