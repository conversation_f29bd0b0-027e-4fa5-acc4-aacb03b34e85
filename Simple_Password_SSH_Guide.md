# 📱 iPhone SSH 密码连接指南

## 🎯 超简单3步连接

### 第1步：确认Tailscale连接 ✅
- **电脑**: Tailscale服务运行中
- **iPhone**: Tailscale应用已连接
- **状态**: 两设备都在同一虚拟网络

### 第2步：在Termius中添加连接

**连接配置**：
```
连接名称: 家里电脑
主机名: *************
端口: 22
用户名: lenovo
认证方式: 密码
密码: [你的Windows登录密码]
```

**操作步骤**：
1. 打开Termius应用
2. 点击右上角 "+" 
3. 选择 "New Host"
4. 填写上述信息
5. 保存连接

### 第3步：连接测试
1. 点击刚创建的连接
2. 输入Windows登录密码
3. 看到 `C:\Users\<USER>\Program Files\Tailscale\tailscale.exe" status
   ```

2. **检查SSH服务**
   ```bash
   Get-Service sshd
   ```

3. **重启SSH服务**
   ```bash
   Restart-Service sshd
   ```

### 密码认证失败？
1. 确认Windows用户名是 `lenovo`
2. 确认密码是当前Windows登录密码
3. 检查密码是否包含特殊字符
4. 尝试在电脑上重新登录验证密码

### Tailscale连接问题？
1. **iPhone端**: 重启Tailscale应用
2. **电脑端**: 重启Tailscale服务
3. **网络**: 切换WiFi或使用移动数据

## 📞 备用方案

如果Tailscale连接失败，可以使用：
1. **传统端口转发**: 主机 `*************:2222`
2. **TeamViewer**: 图形界面远程控制
3. **向日葵**: 国产远程控制软件

## 🎉 完成！

现在你可以：
- 在任何地方通过iPhone连接家里电脑
- 使用熟悉的Windows密码登录
- 享受安全的端到端加密连接
- 无需复杂的网络配置

**保存这个指南到手机，随时查看连接信息！** 📱
