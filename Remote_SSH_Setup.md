# 外地远程SSH连接配置指南

## 当前网络信息
- **家里电脑内网IP**: **************
- **家里公网IP**: *************
- **路由器网关**: ************

## 🚀 快速设置步骤

### 第一步：配置路由器端口转发

1. **登录路由器管理界面**
   ```
   浏览器访问: http://************
   ```
   
2. **找到端口转发设置**
   - 可能叫做："端口转发"、"虚拟服务器"、"NAT转发"、"Port Forwarding"
   
3. **添加转发规则**
   ```
   服务名称: SSH_Remote
   外部端口: 2222
   内部端口: 22  
   内部IP: **************
   协议: TCP
   状态: 启用
   ```

### 第二步：配置Termius连接

在Termius中添加新主机：
```
主机名: *************
端口: 2222
用户名: lenovo
密码: [你的Windows登录密码]
```

### 第三步：测试连接

1. 确保家里的电脑开机且SSH服务运行
2. 在外地使用Termius连接测试

## 🔧 详细配置说明

### 常见路由器配置界面

**TP-Link路由器**:
- 高级设置 → NAT转发 → 虚拟服务器

**华为路由器**:
- 更多功能 → 安全设置 → 虚拟服务器

**小米路由器**:
- 高级设置 → 端口转发

**腾达路由器**:
- 高级设置 → 虚拟服务器

### 安全建议

1. **修改默认SSH端口**
   ```powershell
   # 在Windows上执行
   notepad C:\ProgramData\ssh\sshd_config
   # 找到 #Port 22，改为 Port 2222
   # 重启SSH服务
   Restart-Service sshd
   ```

2. **设置强密码**
   - 确保Windows登录密码足够复杂

3. **限制登录尝试**
   - 考虑安装fail2ban类似工具

## 🌐 动态IP解决方案

如果你家的公网IP经常变化，推荐使用DDNS：

### 花生壳DDNS (推荐中国用户)
1. 注册账号: https://hsk.oray.com
2. 申请免费域名
3. 在路由器中配置花生壳DDNS
4. 在Termius中使用域名而不是IP

### No-IP DDNS (国际服务)
1. 注册账号: https://www.noip.com
2. 创建免费主机名
3. 在路由器中配置No-IP DDNS

## 🔍 故障排查

### 连接超时
- 检查路由器端口转发是否正确配置
- 确认公网IP是否正确
- 检查家里网络是否正常

### 连接被拒绝
- 确认SSH服务在家里电脑上运行
- 检查Windows防火墙设置
- 验证端口号是否匹配

### 认证失败
- 确认用户名密码正确
- 检查Windows用户账户状态

## 📱 Termius配置截图说明

1. **添加主机**
   - 点击Termius右上角"+"
   - 选择"New Host"

2. **填写信息**
   ```
   Alias: 家里电脑
   Hostname: *************
   Username: lenovo
   Port: 2222
   ```

3. **保存并连接**
   - 点击"Save"
   - 点击主机名连接

## ⚠️ 重要提醒

1. **确保家里电脑始终开机**
2. **定期检查公网IP是否变化**
3. **考虑设置Wake-on-LAN远程唤醒**
4. **备份重要的SSH配置**

## 🆘 应急方案

如果SSH连接不上，可以考虑：
1. **TeamViewer** - 图形界面远程控制
2. **向日葵** - 国产远程控制软件
3. **Windows远程桌面** - 需要类似的端口转发设置
