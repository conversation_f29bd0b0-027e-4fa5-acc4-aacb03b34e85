# Termius SSH 远程连接设置指南

## 当前系统状态
✅ OpenSSH 服务器已安装  
✅ SSH 服务正在运行  
✅ 当前内网IP: ************96  
✅ 网关: ************  

## 1. 基本SSH连接设置

### 在Termius中添加新主机
1. 打开Termius应用
2. 点击"+"添加新主机
3. 填写连接信息：
   - **主机名**: ************96 (内网连接)
   - **用户名**: lenovo (你的Windows用户名)
   - **端口**: 22 (默认SSH端口)
   - **密码**: 你的Windows登录密码

### 测试内网连接
在同一局域网内的设备上，使用上述信息即可连接。

## 2. 外网访问设置 (必需 - 适用于你的情况)

### 方案一：路由器端口转发 (推荐)

#### 步骤1: 获取当前公网IP
```bash
# 在你的电脑上执行，获取家里的公网IP
curl ifconfig.me
# 或者访问 http://whatismyipaddress.com
```

#### 步骤2: 配置路由器端口转发
1. 登录家里的路由器管理界面
   - 浏览器访问: http://************
   - 用户名/密码通常在路由器背面标签上
2. 找到"端口转发"、"虚拟服务器"或"NAT转发"设置
3. 添加新规则：
   - **服务名称**: SSH_Remote
   - **外部端口**: 2222 (安全起见，不用默认22)
   - **内部端口**: 22
   - **内部IP**: ************96
   - **协议**: TCP
   - **状态**: 启用

#### 步骤3: 在Termius中配置外网连接
- **主机名**: 你的公网IP (步骤1获取的)
- **端口**: 2222
- **用户名**: lenovo
- **密码**: 你的Windows密码

### 方案二：动态DNS (DDNS) - 解决IP变化问题
如果你家的公网IP会变化，建议使用DDNS：

1. **注册免费DDNS服务**：
   - No-IP (https://www.noip.com)
   - DynDNS
   - 花生壳 (https://hsk.oray.com)

2. **在路由器中配置DDNS**：
   - 找到路由器的DDNS设置
   - 输入你注册的域名和账户信息
   - 启用DDNS服务

3. **在Termius中使用域名连接**：
   - **主机名**: 你的DDNS域名 (如: yourname.ddns.net)
   - **端口**: 2222
   - **用户名**: lenovo

### 方案三：内网穿透工具 (简单但有限制)
如果路由器配置复杂，可以使用内网穿透：

1. **frp (免费开源)**：
   - 需要有公网服务器
   - 配置相对复杂但功能强大

2. **花生壳内网穿透**：
   - 免费版有流量限制
   - 配置简单，适合临时使用

3. **ngrok**：
   - 国外服务，可能网络不稳定
   - 免费版功能有限

## 3. 安全加固建议

### 修改SSH默认端口
1. 编辑SSH配置文件：
   ```
   C:\ProgramData\ssh\sshd_config
   ```
2. 找到 `#Port 22` 行，修改为：
   ```
   Port 2222
   ```
3. 重启SSH服务

### 禁用密码认证，启用密钥认证
1. 生成SSH密钥对
2. 配置公钥认证
3. 禁用密码登录

## 4. 防火墙设置

确保Windows防火墙允许SSH连接：
```powershell
New-NetFirewallRule -Name sshd -DisplayName 'OpenSSH Server (sshd)' -Enabled True -Direction Inbound -Protocol TCP -Action Allow -LocalPort 22
```

## 5. 常见问题排查

### 连接被拒绝
- 检查SSH服务是否运行
- 检查防火墙设置
- 确认端口号正确

### 认证失败
- 确认用户名和密码正确
- 检查用户是否有登录权限

### 网络不通
- 确认IP地址正确
- 检查网络连接
- 验证路由器端口转发设置

## 6. 有用的命令

```bash
# 检查SSH服务状态
Get-Service sshd

# 重启SSH服务
Restart-Service sshd

# 查看SSH连接日志
Get-EventLog -LogName Security -InstanceId 4624,4625 | Select-Object -First 10

# 查看当前SSH连接
netstat -an | findstr :22
```
