# 📱 iPhone SSH 快速连接卡片

## 🚀 3步完成连接

### 第1步：确认Tailscale运行 ✅
- iPhone: 打开Tailscale应用，确保已连接
- 电脑: Tailscale服务正在运行

### 第2步：配置Termius
```
主机名: *************
端口: 22
用户名: lenovo
认证: 密码 (Windows登录密码)
```

### 第3步：连接测试
- 点击连接
- 看到 `C:\Users\<USER>\Program Files\Tailscale\tailscale.exe" status
```

### 重启SSH服务
```bash
Restart-Service sshd
```

### 检查SSH服务
```bash
Get-Service sshd
```

## 📋 连接信息
- **电脑IP**: `*************`
- **iPhone IP**: `************`
- **用户**: `lenovo`
- **密码**: Windows登录密码

## 🆘 故障排查
1. Tailscale未连接 → 重启应用
2. SSH连接失败 → 检查服务状态
3. 密码认证失败 → 确认Windows密码正确

---
**💡 提示**: 保存这个文件到手机，随时查看连接信息！
